import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type TeamsOngoingPoolBracketsStatsQueryParams = {
	eventId: string;
	teamsIds: string[];
};

export const getTeamsOngoingPoolBracketsStatsQuery = ({
	eventId,
	teamsIds,
}: TeamsOngoingPoolBracketsStatsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const teamsIdsValues = teamsIds.map((id) => sqlVars.useValue(Number(id)));

	const query = `
		SELECT DISTINCT ON (rt.roster_team_id)
			rt.roster_team_id,
			pb.pb_stats
		FROM roster_team rt
		JOIN matches m 
			ON m.event_id = rt.event_id
			AND m.division_id = rt.division_id
			AND (m.team1_roster_id = rt.roster_team_id OR m.team2_roster_id = rt.roster_team_id)
		JOIN poolbrackets pb 
			ON pb.event_id = m.event_id
			AND pb.uuid = m.pool_bracket_id
		WHERE 
			rt.event_id = ${sqlVars.useValue(Number(eventId))}
			AND rt.roster_team_id IN (${teamsIdsValues.join(',')})
			AND EXISTS (
				SELECT 1 FROM matches m2
				WHERE m2.pool_bracket_id = m.pool_bracket_id
				AND m2.secs_finished IS NULL
			)
		ORDER BY rt.roster_team_id, m.secs_start ASC
	`;

	return [query, sqlVars.getValues()];
};
