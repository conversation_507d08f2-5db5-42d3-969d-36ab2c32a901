import { Args, Context, Parent, Query, Resolve<PERSON>ield, Resolver } from '@nestjs/graphql';
import { TeamsPoolBrackets } from 'shared/graphql/context';

import { DivisionStanding } from '@/modules/divisionStanding/entities';
import { Match } from '@/modules/match/entities';
import { MatchService } from '@/modules/match/match.service';
import { PoolOrBracketStatItem } from '@/modules/poolBracket/entities';
import { EventKey } from '@/shared/utils/event';

import { DivisionTeamsStandingInputDto } from './dto/division-teams-standing.dto';
import { FavoriteTeamsInputDto } from './dto/favorite-teams.dto';
import { PaginatedDivisionTeamsInputDto } from './dto/paginated-division-teams.dto';
import { QualifiedTeamsInputDto } from './dto/qualified-teams.dto';
import { TeamMatchesOptionsInputDto } from './dto/team-matches-options.dto';
import { PaginatedTeams, Team } from './entities';
import {
	TeamsMatchesLoader,
	TeamsNextMatchLoader,
	TeamsDivisionStandingLoader,
	TeamsFinishedMatchesLoader,
	TeamsUpcomingMatchesLoader,
} from './loaders';
import { TeamService } from './team.service';

type TeamResolverContext = {
	teamsMatchesLoader: ReturnType<TeamsMatchesLoader['create']>;
	teamsNextMatchLoader: ReturnType<TeamsNextMatchLoader['create']>;
	teamsDivisionStandingLoader: ReturnType<TeamsDivisionStandingLoader['create']>;
	teamsFinishedMatchesLoader?: ReturnType<TeamsFinishedMatchesLoader['create']>;
	teamsUpcomingMatchesLoader?: ReturnType<TeamsUpcomingMatchesLoader['create']>;
} & TeamsPoolBrackets;

@Resolver(() => Team)
export class TeamResolver {
	constructor(
		private readonly teamService: TeamService,
		private readonly matchService: MatchService,
		private readonly teamsMatchesLoader: TeamsMatchesLoader,
		private readonly teamsNextMatchLoader: TeamsNextMatchLoader,
		private readonly teamsDivisionStandingLoader: TeamsDivisionStandingLoader,
		private readonly teamsFinishedMatchesLoader: TeamsFinishedMatchesLoader,
		private readonly teamsUpcomingMatchesLoader: TeamsUpcomingMatchesLoader,
	) {}

	@Query(() => [Team])
	favoriteTeams(@Args() args: FavoriteTeamsInputDto): Promise<Team[]> {
		return this.teamService.fetchFavoriteTeams({
			...args,
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
	}

	@Query(() => [Team])
	qualifiedTeams(@Args() args: QualifiedTeamsInputDto): Promise<Team[]> {
		return this.teamService.fetchQualifiedTeams({
			...args,
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
	}

	@Query(() => [Team])
	divisionTeamsStanding(@Args() args: DivisionTeamsStandingInputDto): Promise<Team[]> {
		return this.teamService.fetchDivisionTeamsStanding({
			...args,
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
	}

	@Query(() => PaginatedTeams)
	paginatedDivisionTeams(@Args() args: PaginatedDivisionTeamsInputDto): Promise<PaginatedTeams> {
		return this.teamService.fetchPaginatedDivisionTeams({
			...args,
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
	}

	@ResolveField(() => [Match])
	async matches(
		@Parent() team: Team,
		@Context() context: TeamResolverContext,
		@Args() options: TeamMatchesOptionsInputDto,
	): Promise<Match[]> {
		if (!context.teamsMatchesLoader) {
			context.teamsMatchesLoader = this.teamsMatchesLoader.create(team.event_id);
		}
		const matches = await context.teamsMatchesLoader.load(team);
		return this._filterAndLimitMatches(matches, team, context, options);
	}

	@ResolveField(() => [Match], { name: 'upcoming_matches' })
	async upcomingMatches(
		@Parent() team: Team,
		@Context() context: TeamResolverContext,
		@Args() options: TeamMatchesOptionsInputDto,
	): Promise<Match[]> {
		if (!context.teamsUpcomingMatchesLoader) {
			context.teamsUpcomingMatchesLoader = this.teamsUpcomingMatchesLoader.create(team.event_id);
		}
		const matches = await context.teamsUpcomingMatchesLoader.load(team);
		return this._filterAndLimitMatches(matches, team, context, options);
	}

	@ResolveField(() => [Match], { name: 'finished_matches' })
	async finishedMatches(
		@Parent() team: Team,
		@Context() context: TeamResolverContext,
		@Args() options: TeamMatchesOptionsInputDto,
	): Promise<Match[]> {
		if (!context.teamsFinishedMatchesLoader) {
			context.teamsFinishedMatchesLoader = this.teamsFinishedMatchesLoader.create(team.event_id);
		}
		const matches = await context.teamsFinishedMatchesLoader.load(team);
		return this._filterAndLimitMatches(matches, team, context, options);
	}

	@ResolveField(() => Match, { name: 'next_match', nullable: true })
	nextMatch(@Parent() team: Team, @Context() context: TeamResolverContext): Promise<Match> {
		if (!context.teamsNextMatchLoader) {
			context.teamsNextMatchLoader = this.teamsNextMatchLoader.create(team.event_id);
		}
		return context.teamsNextMatchLoader.load(team);
	}

	@ResolveField(() => DivisionStanding, { name: 'division_standing', nullable: true })
	divisionStanding(@Parent() team: Team, @Context() context: TeamResolverContext): Promise<DivisionStanding> {
		if (!context.teamsDivisionStandingLoader) {
			context.teamsDivisionStandingLoader = this.teamsDivisionStandingLoader.create(team.event_id);
		}
		return context.teamsDivisionStandingLoader.load(team);
	}

	@ResolveField(() => PoolOrBracketStatItem, { name: 'pool_bracket_stat', nullable: true })
	poolBracketStat(@Parent() team: Team, @Context() context: TeamResolverContext): PoolOrBracketStatItem | null {
		if (context.teamsPoolBrackets) {
			// If we resolve the team for the Pool/Bracket, then return the team pb_stat from the resolved Pool/Bracket
			const poolBracket = context.teamsPoolBrackets?.get(team) || null;
			return poolBracket?.pb_stats?.find((stat) => stat.team_id === team.team_id) || null;
		} else {
			// If we resolve the team outside the Pool/Bracket, then return the team pb_stat from its currently active Pool/Bracket
			return null;
		}
	}

	private _filterAndLimitMatches(
		matches: Match[],
		team: Team,
		context: TeamResolverContext,
		{ pb_only, limit, as_team, as_ref }: TeamMatchesOptionsInputDto,
	): Match[] {
		if (pb_only && context.teamsPoolBrackets?.has(team)) {
			const { uuid } = context.teamsPoolBrackets.get(team);
			matches = this.matchService.filterMatchesByPoolBracket(matches, uuid);
		}
		if (as_team) {
			matches = this.matchService.filterMatchesWhereTeamPlays(matches, team.team_id);
		} else if (as_ref) {
			matches = this.matchService.filterMatchesWhereTeamRefs(matches, team.team_id);
		}

		return limit ? matches.slice(0, limit) : matches;
	}
}
